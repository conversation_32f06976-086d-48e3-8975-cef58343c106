/**
 * Lambda function to get user's current subscription
 * Endpoint: GET /stripe/subscription
 */

import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { 
  getAuthenticatedUser, 
  successResponse, 
  errorResponse, 
  handleCORS 
} from '../utils/auth';
import { userRepository, subscriptionRepository } from '../utils/dynamodb';
import { getStripeSubscription } from '../utils/stripe';

export const handler = async (
  event: APIGatewayProxyEvent
): Promise<APIGatewayProxyResult> => {
  console.log('Get subscription request:', JSON.stringify(event, null, 2));

  // Handle CORS preflight
  if (event.httpMethod === 'OPTIONS') {
    return handleCORS();
  }

  try {
    // Authenticate user
    const cognitoUser = getAuthenticatedUser(event);
    
    // Get user from database
    const user = await userRepository.getUser(cognitoUser.sub);
    
    if (!user) {
      return errorResponse('User not found', 404);
    }

    // Get active subscription
    const subscription = await subscriptionRepository.getActiveSubscription(user.userId);
    
    if (!subscription) {
      return errorResponse('No active subscription found', 404);
    }

    // Optionally sync with Stripe to get latest status
    try {
      const stripeSubscription = await getStripeSubscription(subscription.stripeSubscriptionId);
      
      // Update local subscription if status has changed
      if (stripeSubscription.status !== subscription.status) {
        const updatedSubscription = await subscriptionRepository.updateSubscription(
          subscription.userId,
          subscription.subscriptionId,
          {
            status: stripeSubscription.status as any,
            currentPeriodStart: (stripeSubscription as any).current_period_start,
            currentPeriodEnd: (stripeSubscription as any).current_period_end,
            cancelAtPeriodEnd: stripeSubscription.cancel_at_period_end || false,
          }
        );
        
        return successResponse(updatedSubscription);
      }
    } catch (stripeError) {
      console.warn('Failed to sync with Stripe, returning cached subscription:', stripeError);
    }

    return successResponse(subscription);

  } catch (error) {
    console.error('Error getting subscription:', error);
    
    if (error instanceof Error) {
      if (error.message.includes('No authorization token')) {
        return errorResponse('Authentication required', 401);
      }
      if (error.message.includes('Invalid token') || error.message.includes('Token expired')) {
        return errorResponse('Invalid or expired token', 401);
      }
      
      return errorResponse(error.message, 500);
    }
    
    return errorResponse('Internal server error', 500);
  }
};
